"""
Tests for function tools.
"""

import asyncio
import platform
from unittest.mock import Mo<PERSON>, patch, AsyncMock

import pytest

from src.tools.base import ToolStatus
from src.tools.bash_tool import BashTool
from src.tools.web_search_tool import WebSearchTool


@pytest.mark.unit
class TestBashTool:
    """Test BashTool functionality."""
    
    def test_tool_properties(self):
        """Test basic tool properties."""
        tool = BashTool()
        
        assert tool.name == "bash"
        assert isinstance(tool.description, str)
        assert len(tool.description) > 0
        assert isinstance(tool.parameters, dict)
        assert "command" in tool.parameters["properties"]
        assert tool.can_run_parallel is False
        assert tool.is_destructive is True
    
    def test_parameter_validation(self):
        """Test parameter validation."""
        tool = BashTool()
        
        # Valid parameters
        errors = tool.validate_parameters(command="echo hello")
        assert len(errors) == 0
        
        # Missing required parameter
        errors = tool.validate_parameters()
        assert len(errors) > 0
        assert any("Missing required parameter: command" in error for error in errors)
    
    def test_dangerous_command_detection(self):
        """Test dangerous command detection."""
        tool = BashTool()
        
        # Safe commands
        assert not tool._is_dangerous_command("ls -la")
        assert not tool._is_dangerous_command("echo hello")
        assert not tool._is_dangerous_command("cat file.txt")
        
        # Dangerous commands
        assert tool._is_dangerous_command("rm -rf /")
        assert tool._is_dangerous_command("sudo rm file")
        assert tool._is_dangerous_command("chmod 777 file")
        assert tool._is_dangerous_command("shutdown now")
    
    def test_system_modifying_detection(self):
        """Test system-modifying command detection."""
        tool = BashTool()
        
        # Non-modifying commands
        assert not tool._is_system_modifying("ls -la")
        assert not tool._is_system_modifying("cat file.txt")
        
        # System-modifying commands
        assert tool._is_system_modifying("apt install package")
        assert tool._is_system_modifying("pip install package")
        assert tool._is_system_modifying("npm install package")
        assert tool._is_system_modifying("docker run image")
    
    def test_command_safety_analysis(self):
        """Test command safety analysis."""
        tool = BashTool()
        
        # Safe command
        analysis = tool._analyze_command_safety("ls -la")
        assert not analysis["is_dangerous"]
        assert not analysis["requires_confirmation"]
        assert len(analysis["warnings"]) == 0
        
        # Dangerous command
        analysis = tool._analyze_command_safety("rm -rf /")
        assert analysis["requires_confirmation"]
        assert len(analysis["warnings"]) > 0
        
        # System-modifying command
        analysis = tool._analyze_command_safety("apt install package")
        assert analysis["is_system_modifying"]
        assert len(analysis["warnings"]) > 0
    
    @pytest.mark.asyncio
    async def test_successful_execution(self):
        """Test successful command execution."""
        tool = BashTool()
        
        # Simple echo command should work on all platforms
        if platform.system() == "Windows":
            command = "echo hello"
        else:
            command = "echo hello"
        
        result = await tool.execute(command=command)
        
        assert result.status == ToolStatus.SUCCESS
        assert "hello" in result.output.lower()
        assert result.error is None
        assert result.execution_time is not None
        assert result.execution_time > 0
    
    @pytest.mark.asyncio
    async def test_command_failure(self):
        """Test command execution failure."""
        tool = BashTool()
        
        # Command that should fail
        result = await tool.execute(command="nonexistent_command_12345")
        
        assert result.status == ToolStatus.ERROR
        assert result.error is not None
        assert result.execution_time is not None
    
    @pytest.mark.asyncio
    async def test_parameter_validation_error(self):
        """Test parameter validation error handling."""
        tool = BashTool()
        
        # Missing required parameter
        result = await tool.execute()
        
        assert result.status == ToolStatus.ERROR
        assert "Parameter validation failed" in result.error
    
    @pytest.mark.asyncio
    @patch('asyncio.create_subprocess_exec')
    async def test_timeout_handling(self, mock_subprocess):
        """Test command timeout handling."""
        tool = BashTool()
        
        # Mock a process that times out
        mock_process = Mock()
        mock_process.communicate = AsyncMock()
        mock_process.communicate.side_effect = asyncio.TimeoutError()
        mock_process.kill = Mock()
        mock_process.wait = AsyncMock()
        mock_subprocess.return_value = mock_process
        
        result = await tool.execute(command="sleep 10", timeout=1)
        
        assert result.status == ToolStatus.TIMEOUT
        assert "timed out" in result.error.lower()
        mock_process.kill.assert_called_once()


@pytest.mark.unit
class TestWebSearchTool:
    """Test WebSearchTool functionality."""
    
    def test_tool_properties(self):
        """Test basic tool properties."""
        tool = WebSearchTool()
        
        assert tool.name == "web_search"
        assert isinstance(tool.description, str)
        assert len(tool.description) > 0
        assert isinstance(tool.parameters, dict)
        assert "query" in tool.parameters["properties"]
        assert tool.can_run_parallel is True
        assert tool.is_destructive is False
    
    def test_parameter_validation(self):
        """Test parameter validation."""
        tool = WebSearchTool()
        
        # Valid parameters
        errors = tool.validate_parameters(query="test query")
        assert len(errors) == 0
        
        # Missing required parameter
        errors = tool.validate_parameters()
        assert len(errors) > 0
        assert any("Missing required parameter: query" in error for error in errors)
        
        # Invalid max_results
        errors = tool.validate_parameters(query="test", max_results=15)
        assert len(errors) == 0  # Should be clamped, not error
    
    def test_cache_key_generation(self):
        """Test cache key generation."""
        tool = WebSearchTool()
        
        key1 = tool._get_cache_key("test query", max_results=5)
        key2 = tool._get_cache_key("test query", max_results=5)
        key3 = tool._get_cache_key("different query", max_results=5)
        key4 = tool._get_cache_key("test query", max_results=10)
        
        assert key1 == key2  # Same parameters should generate same key
        assert key1 != key3  # Different query should generate different key
        assert key1 != key4  # Different max_results should generate different key
    
    def test_cache_validity(self):
        """Test cache validity checking."""
        tool = WebSearchTool()
        
        import time
        
        # Fresh cache entry
        cache_entry = {"timestamp": time.time()}
        assert tool._is_cache_valid(cache_entry)
        
        # Old cache entry
        cache_entry = {"timestamp": time.time() - 7200}  # 2 hours ago
        assert not tool._is_cache_valid(cache_entry)
    
    def test_result_formatting(self):
        """Test search result formatting."""
        tool = WebSearchTool()
        
        # Empty results
        formatted = tool._format_results([])
        assert "No search results found" in formatted
        
        # Sample results
        results = [
            {
                "title": "Test Title 1",
                "snippet": "Test description 1",
                "url": "https://example.com/1",
                "source": "Example"
            },
            {
                "title": "Test Title 2",
                "snippet": "Test description 2",
                "url": "https://example.com/2",
                "source": "Example"
            }
        ]
        
        formatted = tool._format_results(results)
        assert "Test Title 1" in formatted
        assert "Test Title 2" in formatted
        assert "Test description 1" in formatted
        assert "https://example.com/1" in formatted
    
    @pytest.mark.asyncio
    async def test_empty_query_handling(self):
        """Test empty query handling."""
        tool = WebSearchTool()
        
        result = await tool.execute(query="")
        
        assert result.status == ToolStatus.ERROR
        assert "cannot be empty" in result.error.lower()
    
    @pytest.mark.asyncio
    async def test_parameter_validation_error(self):
        """Test parameter validation error handling."""
        tool = WebSearchTool()
        
        # Missing required parameter
        result = await tool.execute()
        
        assert result.status == ToolStatus.ERROR
        assert "Parameter validation failed" in result.error
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient.get')
    async def test_successful_search(self, mock_get):
        """Test successful web search."""
        tool = WebSearchTool()
        
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "Abstract": "Test abstract",
            "AbstractURL": "https://example.com",
            "AbstractSource": "Test Source",
            "Heading": "Test Heading",
            "RelatedTopics": [
                {
                    "Text": "Related topic 1",
                    "FirstURL": "https://example.com/related1"
                }
            ]
        }
        mock_get.return_value = mock_response
        
        result = await tool.execute(query="test query")
        
        assert result.status == ToolStatus.SUCCESS
        assert "Test Heading" in result.output
        assert "Test abstract" in result.output
        assert result.metadata["query"] == "test query"
        assert result.metadata["results_count"] >= 1
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient.get')
    async def test_api_error_handling(self, mock_get):
        """Test API error handling."""
        tool = WebSearchTool()
        
        # Mock API error
        mock_response = Mock()
        mock_response.status_code = 429  # Rate limit
        mock_get.return_value = mock_response
        
        result = await tool.execute(query="test query")
        
        assert result.status == ToolStatus.ERROR
        assert "rate limit" in result.error.lower() or "service error" in result.error.lower()
    
    @pytest.mark.asyncio
    async def test_cache_functionality(self):
        """Test caching functionality."""
        tool = WebSearchTool()
        
        # Mock successful search
        with patch.object(tool, '_search_duckduckgo') as mock_search:
            mock_search.return_value = [
                {
                    "title": "Test Result",
                    "snippet": "Test description",
                    "url": "https://example.com",
                    "source": "Test"
                }
            ]
            
            # First search - should call API
            result1 = await tool.execute(query="test query")
            assert result1.status == ToolStatus.SUCCESS
            assert not result1.metadata.get("cached", False)
            mock_search.assert_called_once()
            
            # Second search - should use cache
            mock_search.reset_mock()
            result2 = await tool.execute(query="test query")
            assert result2.status == ToolStatus.SUCCESS
            assert result2.metadata.get("cached", False)
            mock_search.assert_not_called()
