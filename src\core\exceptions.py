"""
Custom exception classes for Arien AI.

This module defines a hierarchy of exceptions that provide clear error classification
and enable intelligent retry logic throughout the system.
"""

from typing import Optional, Dict, Any


class ArienError(Exception):
    """Base exception class for all Arien AI errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class ConfigurationError(ArienError):
    """Raised when there's a configuration-related error."""
    pass


class LLMError(ArienError):
    """Base class for LLM-related errors."""
    pass


class ToolError(ArienError):
    """Base class for tool execution errors."""
    pass


class RetryableError(ArienError):
    """
    Base class for errors that can be retried.
    
    These are typically temporary issues like network timeouts,
    rate limiting, or temporary service unavailability.
    """
    pass


class NonRetryableError(ArienError):
    """
    Base class for errors that should not be retried.
    
    These are typically permanent issues like authentication failures,
    invalid input, or configuration errors.
    """
    pass


# LLM-specific errors
class LLMConnectionError(LLMError, RetryableError):
    """Raised when unable to connect to LLM provider."""
    pass


class LLMRateLimitError(LLMError, RetryableError):
    """Raised when LLM provider rate limit is exceeded."""
    pass


class LLMAuthenticationError(LLMError, NonRetryableError):
    """Raised when LLM provider authentication fails."""
    pass


class LLMInvalidRequestError(LLMError, NonRetryableError):
    """Raised when LLM provider receives invalid request."""
    pass


class LLMTimeoutError(LLMError, RetryableError):
    """Raised when LLM provider request times out."""
    pass


# Tool-specific errors
class BashExecutionError(ToolError):
    """Raised when bash command execution fails."""
    
    def __init__(
        self,
        message: str,
        command: str,
        exit_code: int,
        stdout: str = "",
        stderr: str = "",
        **kwargs
    ) -> None:
        super().__init__(message, **kwargs)
        self.command = command
        self.exit_code = exit_code
        self.stdout = stdout
        self.stderr = stderr


class BashTimeoutError(BashExecutionError, RetryableError):
    """Raised when bash command times out."""
    pass


class WebSearchError(ToolError):
    """Base class for web search errors."""
    pass


class WebSearchRateLimitError(WebSearchError, RetryableError):
    """Raised when web search API rate limit is exceeded."""
    pass


class WebSearchConnectionError(WebSearchError, RetryableError):
    """Raised when unable to connect to web search API."""
    pass


class WebSearchInvalidQueryError(WebSearchError, NonRetryableError):
    """Raised when web search query is invalid."""
    pass


def classify_http_error(status_code: int, response_text: str = "") -> type:
    """
    Classify HTTP errors into retryable or non-retryable categories.
    
    Args:
        status_code: HTTP status code
        response_text: Response body text for additional context
        
    Returns:
        Exception class to use for this error
    """
    if status_code == 401:
        return LLMAuthenticationError
    elif status_code == 429:
        return LLMRateLimitError
    elif status_code in (400, 422):
        return LLMInvalidRequestError
    elif status_code >= 500:
        return LLMConnectionError
    elif status_code in (502, 503, 504):
        return LLMConnectionError
    else:
        # Default to retryable for unknown errors
        return LLMConnectionError


def is_retryable_error(error: Exception) -> bool:
    """
    Check if an error is retryable.
    
    Args:
        error: Exception to check
        
    Returns:
        True if error should be retried, False otherwise
    """
    return isinstance(error, RetryableError)
