# Installation Guide

This guide provides detailed installation instructions for Arien AI across different platforms and environments.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Installation](#quick-installation)
- [Manual Installation](#manual-installation)
- [Platform-Specific Instructions](#platform-specific-instructions)
- [Configuration](#configuration)
- [Verification](#verification)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Operating System**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 18.04+, CentOS 7+, etc.)
- **Python**: Version 3.8 or higher
- **Memory**: Minimum 512MB RAM available
- **Storage**: At least 100MB free disk space
- **Network**: Internet connection for API access and web search

### Required Software

1. **Python 3.8+**
   - Download from [python.org](https://python.org)
   - Ensure `pip` is included (usually installed by default)

2. **Git**
   - Download from [git-scm.com](https://git-scm.com)
   - Required for cloning the repository

3. **Terminal/Command Prompt**
   - Windows: PowerShell 5.1+ or Command Prompt
   - macOS: Terminal.app or iTerm2
   - Linux: Any terminal emulator

### Optional Software

- **Ollama** (for local models): Download from [ollama.ai](https://ollama.ai)
- **curl** (for installation script): Usually pre-installed on macOS/Linux

## Quick Installation

### Option 1: Automated Script (Recommended)

#### Linux/macOS/WSL
```bash
curl -sSL https://raw.githubusercontent.com/arien-ai/arien-ai/main/install.sh | bash
```

#### Windows PowerShell
```powershell
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/arien-ai/arien-ai/main/install.ps1" -OutFile "install.ps1"
.\install.ps1
```

### Option 2: Package Manager (Coming Soon)

```bash
# pip (coming soon)
pip install arien-ai

# conda (coming soon)
conda install -c conda-forge arien-ai

# homebrew (coming soon)
brew install arien-ai
```

## Manual Installation

### Step 1: Clone Repository

```bash
git clone https://github.com/arien-ai/arien-ai.git
cd arien-ai
```

### Step 2: Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

### Step 3: Install Dependencies

```bash
# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt

# Install Arien AI in development mode
pip install -e .
```

### Step 4: Create Launcher (Optional)

#### Linux/macOS
```bash
# Create bin directory
mkdir -p ~/.local/bin

# Create launcher script
cat > ~/.local/bin/arien << 'EOF'
#!/bin/bash
source /path/to/arien-ai/venv/bin/activate
exec python -m src.main "$@"
EOF

# Make executable
chmod +x ~/.local/bin/arien

# Add to PATH (add to ~/.bashrc or ~/.zshrc)
export PATH="$HOME/.local/bin:$PATH"
```

#### Windows
```batch
@REM Create launcher batch file
echo @echo off > %USERPROFILE%\.local\bin\arien.bat
echo call "C:\path\to\arien-ai\venv\Scripts\activate.bat" >> %USERPROFILE%\.local\bin\arien.bat
echo python -m src.main %* >> %USERPROFILE%\.local\bin\arien.bat

@REM Add to PATH (via System Properties > Environment Variables)
```

## Platform-Specific Instructions

### Windows 10/11

#### Using Windows Subsystem for Linux (WSL)
1. Install WSL2: `wsl --install`
2. Install Ubuntu or preferred Linux distribution
3. Follow Linux installation instructions within WSL

#### Native Windows Installation
1. Install Python from Microsoft Store or python.org
2. Install Git for Windows
3. Use PowerShell for installation
4. Consider using Windows Terminal for better experience

### macOS

#### Intel Macs
```bash
# Install Homebrew (if not already installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Python and Git
brew install python git

# Follow standard installation
```

#### Apple Silicon (M1/M2) Macs
```bash
# Ensure you're using ARM64 Python
python --version
arch

# If using Rosetta Python, install native ARM64 version
brew install python

# Follow standard installation
```

### Linux

#### Ubuntu/Debian
```bash
# Update package list
sudo apt update

# Install Python and Git
sudo apt install python3 python3-pip python3-venv git curl

# Follow standard installation
```

#### CentOS/RHEL/Fedora
```bash
# CentOS/RHEL
sudo yum install python3 python3-pip git curl

# Fedora
sudo dnf install python3 python3-pip git curl

# Follow standard installation
```

#### Arch Linux
```bash
# Install dependencies
sudo pacman -S python python-pip git curl

# Follow standard installation
```

## Configuration

### Environment Variables

Create a `.env` file in the installation directory:

```bash
# Copy example configuration
cp .env.example .env

# Edit configuration
nano .env  # or your preferred editor
```

### Required Configuration

1. **Deepseek API Key** (for Deepseek provider):
```bash
export DEEPSEEK_API_KEY="your-api-key-here"
```

2. **Provider Selection**:
```bash
# For Deepseek (default)
export ARIEN_LLM_PROVIDER="deepseek"
export ARIEN_LLM_MODEL="deepseek-chat"

# For Ollama (local)
export ARIEN_LLM_PROVIDER="ollama"
export ARIEN_LLM_MODEL="llama2"
```

### Optional Configuration

See [Configuration Guide](CONFIGURATION.md) for detailed configuration options.

## Verification

### Test Installation

```bash
# Check version
arien version

# Validate configuration
arien validate

# Test basic functionality
arien run "echo 'Hello, Arien AI!'"
```

### Interactive Test

```bash
# Start interactive mode
arien

# Try these commands:
# - "List files in current directory"
# - "Search for Python 3.12 features"
# - "/help" for available commands
# - "quit" to exit
```

## Troubleshooting

### Common Issues

#### Python Version Issues
```bash
# Check Python version
python --version
python3 --version

# Use specific Python version
python3.8 -m venv venv
python3.9 -m venv venv
```

#### Permission Issues (Linux/macOS)
```bash
# Fix permissions for local bin
chmod +x ~/.local/bin/arien

# Add to PATH if not already
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

#### Virtual Environment Issues
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

#### API Connection Issues
```bash
# Test API key
export DEEPSEEK_API_KEY="your-key"
arien validate

# Check network connectivity
curl -I https://api.deepseek.com
```

#### Windows-Specific Issues

1. **PowerShell Execution Policy**:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

2. **Long Path Support**:
   - Enable long path support in Windows settings
   - Or use shorter installation path

3. **Antivirus Interference**:
   - Add installation directory to antivirus exclusions

### Getting Help

If you encounter issues not covered here:

1. Check the [FAQ](FAQ.md)
2. Search [GitHub Issues](https://github.com/arien-ai/arien-ai/issues)
3. Create a new issue with:
   - Operating system and version
   - Python version
   - Error messages
   - Steps to reproduce

### Logs and Debugging

Enable debug logging for troubleshooting:

```bash
# Set debug log level
export ARIEN_LOG_LEVEL=DEBUG

# Run with verbose output
arien --log-level DEBUG run "test command"

# Check log files (if configured)
tail -f ~/.arien-ai/logs/arien.log
```

## Next Steps

After successful installation:

1. Read the [User Guide](USER_GUIDE.md)
2. Explore [Configuration Options](CONFIGURATION.md)
3. Check out [Examples](EXAMPLES.md)
4. Join the [Community](https://github.com/arien-ai/arien-ai/discussions)

---

**Need more help?** Visit our [Documentation](https://docs.arien-ai.com) or [GitHub Discussions](https://github.com/arien-ai/arien-ai/discussions).
