# Arien AI Installation Script for Windows PowerShell
# Supports Windows 10/11 with PowerShell 5.1+

param(
    [string]$InstallPath = "$env:USERPROFILE\.arien-ai",
    [switch]$Force,
    [switch]$Help
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
}

# Configuration
$RepoUrl = "https://github.com/arien-ai/arien-ai.git"
$VenvDir = Join-Path $InstallPath "venv"
$BinDir = Join-Path $env:USERPROFILE ".local\bin"

function Write-Header {
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor $Colors.Blue
    Write-Host "║                        Arien AI Installer                   ║" -ForegroundColor $Colors.Blue
    Write-Host "║          Sophisticated AI-powered CLI Terminal System       ║" -ForegroundColor $Colors.Blue
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor $Colors.Blue
}

function Write-Step {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Show-Help {
    Write-Host @"
Arien AI Installation Script for Windows

USAGE:
    .\install.ps1 [OPTIONS]

OPTIONS:
    -InstallPath <path>    Installation directory (default: $env:USERPROFILE\.arien-ai)
    -Force                 Force reinstallation (removes existing installation)
    -Help                  Show this help message

EXAMPLES:
    .\install.ps1                                    # Standard installation
    .\install.ps1 -Force                             # Force reinstallation
    .\install.ps1 -InstallPath "C:\Tools\arien-ai"   # Custom installation path

REQUIREMENTS:
    - Windows 10/11
    - PowerShell 5.1 or higher
    - Python 3.8 or higher
    - Git for Windows
    - Internet connection

"@ -ForegroundColor $Colors.Cyan
}

function Test-Requirements {
    Write-Step "Checking system requirements..."
    
    # Check PowerShell version
    $PSVersion = $PSVersionTable.PSVersion
    if ($PSVersion.Major -lt 5 -or ($PSVersion.Major -eq 5 -and $PSVersion.Minor -lt 1)) {
        Write-Error "PowerShell 5.1 or higher required, found $($PSVersion.ToString())"
        exit 1
    }
    Write-Success "PowerShell $($PSVersion.ToString()) found"
    
    # Check Python
    try {
        $PythonVersion = & python --version 2>&1
        if ($PythonVersion -match "Python (\d+)\.(\d+)") {
            $Major = [int]$Matches[1]
            $Minor = [int]$Matches[2]
            if ($Major -eq 3 -and $Minor -ge 8) {
                Write-Success "Python $($Matches[0]) found"
                $script:PythonCmd = "python"
            } else {
                Write-Error "Python 3.8+ required, found $($Matches[0])"
                exit 1
            }
        } else {
            throw "Unable to parse Python version"
        }
    } catch {
        Write-Error "Python not found. Please install Python 3.8 or higher from https://python.org"
        exit 1
    }
    
    # Check pip
    try {
        & $script:PythonCmd -m pip --version | Out-Null
        Write-Success "pip found"
    } catch {
        Write-Error "pip not found. Please ensure pip is installed with Python."
        exit 1
    }
    
    # Check git
    try {
        & git --version | Out-Null
        Write-Success "Git found"
    } catch {
        Write-Error "Git not found. Please install Git for Windows from https://git-scm.com"
        exit 1
    }
    
    Write-Success "All requirements satisfied"
}

function Backup-ExistingInstallation {
    if (Test-Path $InstallPath) {
        if ($Force) {
            Write-Step "Removing existing installation..."
            Remove-Item $InstallPath -Recurse -Force
            Write-Success "Existing installation removed"
        } else {
            Write-Step "Backing up existing installation..."
            $BackupPath = "$InstallPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            Move-Item $InstallPath $BackupPath
            Write-Success "Existing installation backed up to $BackupPath"
        }
    }
}

function Install-ArienAI {
    Write-Step "Installing Arien AI..."
    
    # Create installation directory
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    Set-Location $InstallPath
    
    # Clone repository
    Write-Step "Cloning repository..."
    & git clone $RepoUrl .
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to clone repository"
        exit 1
    }
    
    # Create virtual environment
    Write-Step "Creating virtual environment..."
    & $script:PythonCmd -m venv $VenvDir
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create virtual environment"
        exit 1
    }
    
    # Activate virtual environment
    $ActivateScript = Join-Path $VenvDir "Scripts\Activate.ps1"
    & $ActivateScript
    
    # Upgrade pip
    Write-Step "Upgrading pip..."
    & python -m pip install --upgrade pip
    
    # Install dependencies
    Write-Step "Installing dependencies..."
    & pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install dependencies"
        exit 1
    }
    
    # Install Arien AI
    Write-Step "Installing Arien AI..."
    & pip install -e .
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install Arien AI"
        exit 1
    }
    
    Write-Success "Arien AI installed successfully"
}

function New-LauncherScript {
    Write-Step "Creating launcher script..."
    
    # Create bin directory
    New-Item -ItemType Directory -Path $BinDir -Force | Out-Null
    
    # Create batch launcher
    $BatchLauncher = Join-Path $BinDir "arien.bat"
    $BatchContent = @"
@echo off
call "$VenvDir\Scripts\activate.bat"
python -m src.main %*
"@
    Set-Content -Path $BatchLauncher -Value $BatchContent
    
    # Create PowerShell launcher
    $PSLauncher = Join-Path $BinDir "arien.ps1"
    $PSContent = @"
# Arien AI PowerShell Launcher
& "$VenvDir\Scripts\Activate.ps1"
& python -m src.main @args
"@
    Set-Content -Path $PSLauncher -Value $PSContent
    
    Write-Success "Launcher scripts created in $BinDir"
}

function Set-PathEnvironment {
    Write-Step "Setting up PATH environment..."
    
    # Get current user PATH
    $CurrentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    
    # Check if bin directory is already in PATH
    if ($CurrentPath -notlike "*$BinDir*") {
        # Add bin directory to PATH
        $NewPath = "$BinDir;$CurrentPath"
        [Environment]::SetEnvironmentVariable("PATH", $NewPath, "User")
        Write-Success "Added $BinDir to user PATH"
        
        # Update current session PATH
        $env:PATH = "$BinDir;$env:PATH"
    } else {
        Write-Success "PATH already configured"
    }
}

function New-ConfigTemplate {
    Write-Step "Creating configuration template..."
    
    $ConfigFile = Join-Path $InstallPath ".env.example"
    $ConfigContent = @"
# Arien AI Configuration Template
# Copy this file to .env and customize as needed

# LLM Provider Configuration
DEEPSEEK_API_KEY=your-deepseek-api-key-here
ARIEN_LLM_PROVIDER=deepseek
ARIEN_LLM_MODEL=deepseek-chat
ARIEN_MAX_TOKENS=4096
ARIEN_TEMPERATURE=0.7

# Tool Configuration
ARIEN_BASH_ENABLED=true
ARIEN_WEB_SEARCH_ENABLED=true
ARIEN_BASH_TIMEOUT=300

# UI Configuration
ARIEN_SHOW_PROGRESS=true
ARIEN_CONFIRM_DESTRUCTIVE=true
ARIEN_COLOR_OUTPUT=true

# System Configuration
ARIEN_LOG_LEVEL=INFO
ARIEN_MAX_RETRIES=3
"@
    Set-Content -Path $ConfigFile -Value $ConfigContent
    Write-Success "Configuration template created at $ConfigFile"
}

function Test-Installation {
    Write-Step "Running post-installation checks..."
    
    # Activate virtual environment
    $ActivateScript = Join-Path $VenvDir "Scripts\Activate.ps1"
    & $ActivateScript
    
    # Test installation
    try {
        & python -m src.main version | Out-Null
        Write-Success "Installation test passed"
    } catch {
        Write-Error "Installation test failed"
        exit 1
    }
    
    # Check launcher scripts
    $BatchLauncher = Join-Path $BinDir "arien.bat"
    $PSLauncher = Join-Path $BinDir "arien.ps1"
    
    if ((Test-Path $BatchLauncher) -and (Test-Path $PSLauncher)) {
        Write-Success "Launcher scripts available"
    } else {
        Write-Error "Launcher scripts not found"
        exit 1
    }
}

function Write-CompletionMessage {
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor $Colors.Green
    Write-Host "║                   Installation Complete!                    ║" -ForegroundColor $Colors.Green
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor $Colors.Green
    
    Write-Host "`nNext Steps:" -ForegroundColor $Colors.Blue
    Write-Host "1. Restart PowerShell or open a new terminal"
    Write-Host "2. Set your Deepseek API key: `$env:DEEPSEEK_API_KEY='your-key'"
    Write-Host "3. Run Arien AI: arien"
    
    Write-Host "`nConfiguration:" -ForegroundColor $Colors.Blue
    Write-Host "• Installation directory: $InstallPath"
    Write-Host "• Configuration template: $InstallPath\.env.example"
    Write-Host "• Launcher scripts: $BinDir"
    
    Write-Host "`nCommands:" -ForegroundColor $Colors.Blue
    Write-Host "• arien                    # Interactive mode"
    Write-Host "• arien run 'command'      # Single command mode"
    Write-Host "• arien config             # Show configuration"
    Write-Host "• arien validate           # Validate setup"
    
    Write-Host "`nNeed help? Visit: https://github.com/arien-ai/arien-ai" -ForegroundColor $Colors.Yellow
}

# Main installation function
function Install-Main {
    Write-Header
    
    if ($Help) {
        Show-Help
        return
    }
    
    # Check if running as administrator
    $IsAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    if ($IsAdmin) {
        Write-Warning "Running as Administrator. Consider running as regular user for user-level installation."
    }
    
    Test-Requirements
    Backup-ExistingInstallation
    Install-ArienAI
    New-LauncherScript
    Set-PathEnvironment
    New-ConfigTemplate
    Test-Installation
    Write-CompletionMessage
}

# Handle Ctrl+C
$null = Register-EngineEvent PowerShell.Exiting -Action {
    Write-Host "`nInstallation interrupted" -ForegroundColor $Colors.Red
}

# Run main installation
try {
    Install-Main
} catch {
    Write-Error "Installation failed: $($_.Exception.Message)"
    exit 1
}
