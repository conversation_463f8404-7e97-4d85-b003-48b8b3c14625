"""
Deepseek LLM provider implementation.

This module implements the Deepseek API integration with support for both
deepseek-chat and deepseek-reasoner models, including streaming and function calling.
"""

import json
import logging
from typing import List, Dict, Any, Optional, AsyncIterator

import httpx

from src.providers.base import BaseLLMProvider, LLMResponse, Message, ToolCall
from src.core.exceptions import (
    LLMConnectionError,
    LLMAuthenticationError,
    LLMRateLimitError,
    LLMInvalidRequestError,
    LLMTimeoutError,
    classify_http_error,
)
from src.core.retry import retry_on_failure

logger = logging.getLogger(__name__)


class DeepseekProvider(BaseLLMProvider):
    """
    Deepseek LLM provider implementation.
    
    Supports both deepseek-chat and deepseek-reasoner models with
    streaming responses and function calling capabilities.
    """
    
    def __init__(
        self,
        api_key: str,
        model: str = "deepseek-chat",
        base_url: str = "https://api.deepseek.com/v1",
        max_tokens: int = 4096,
        temperature: float = 0.7,
        timeout: int = 60,
        **kwargs
    ) -> None:
        """
        Initialize Deepseek provider.
        
        Args:
            api_key: Deepseek API key
            model: Model name (deepseek-chat or deepseek-reasoner)
            base_url: API base URL
            max_tokens: Maximum tokens in response
            temperature: Sampling temperature
            timeout: Request timeout in seconds
        """
        super().__init__(**kwargs)
        
        if not api_key:
            raise LLMAuthenticationError("Deepseek API key is required")
        
        self.api_key = api_key
        self.model = model
        self.base_url = base_url.rstrip("/")
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.timeout = timeout
        
        # Create HTTP client
        self.client = httpx.AsyncClient(
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            },
            timeout=httpx.Timeout(timeout),
        )
    
    @retry_on_failure(max_retries=3)
    async def generate_response(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Generate a response from Deepseek.
        
        Args:
            messages: List of conversation messages
            tools: Optional list of available tools
            **kwargs: Additional parameters
            
        Returns:
            LLM response
        """
        try:
            # Prepare request payload
            payload = {
                "model": self.model,
                "messages": self.format_messages(messages),
                "max_tokens": kwargs.get("max_tokens", self.max_tokens),
                "temperature": kwargs.get("temperature", self.temperature),
                "stream": False,
            }
            
            # Add tools if provided and supported
            if tools and self.supports_tools:
                payload["tools"] = tools
                payload["tool_choice"] = "auto"
            
            logger.debug(f"Sending request to Deepseek: {self.model}")
            
            # Make API request
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                json=payload,
            )
            
            # Handle HTTP errors
            if response.status_code != 200:
                await self._handle_http_error(response)
            
            # Parse response
            data = response.json()
            choice = data["choices"][0]
            message = choice["message"]
            
            # Extract content and tool calls
            content = message.get("content", "")
            tool_calls = []
            
            if "tool_calls" in message:
                tool_calls = self.parse_tool_calls(message["tool_calls"])
            
            return LLMResponse(
                content=content,
                tool_calls=tool_calls,
                finish_reason=choice.get("finish_reason", "stop"),
                usage=data.get("usage"),
                model=data.get("model"),
            )
            
        except httpx.TimeoutException:
            raise LLMTimeoutError("Request to Deepseek timed out")
        except httpx.ConnectError:
            raise LLMConnectionError("Failed to connect to Deepseek")
        except Exception as e:
            if isinstance(e, (LLMConnectionError, LLMAuthenticationError, LLMRateLimitError, LLMInvalidRequestError, LLMTimeoutError)):
                raise
            raise LLMConnectionError(f"Unexpected error: {e}")
    
    async def stream_response(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncIterator[str]:
        """
        Stream response from Deepseek.
        
        Args:
            messages: List of conversation messages
            tools: Optional list of available tools
            **kwargs: Additional parameters
            
        Yields:
            Response chunks as they arrive
        """
        try:
            # Prepare request payload
            payload = {
                "model": self.model,
                "messages": self.format_messages(messages),
                "max_tokens": kwargs.get("max_tokens", self.max_tokens),
                "temperature": kwargs.get("temperature", self.temperature),
                "stream": True,
            }
            
            # Add tools if provided and supported
            if tools and self.supports_tools:
                payload["tools"] = tools
                payload["tool_choice"] = "auto"
            
            logger.debug(f"Starting stream from Deepseek: {self.model}")
            
            # Make streaming request
            async with self.client.stream(
                "POST",
                f"{self.base_url}/chat/completions",
                json=payload,
            ) as response:
                
                # Handle HTTP errors
                if response.status_code != 200:
                    await self._handle_http_error(response)
                
                # Process streaming response
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix
                        
                        if data_str.strip() == "[DONE]":
                            break
                        
                        try:
                            data = json.loads(data_str)
                            choice = data["choices"][0]
                            delta = choice.get("delta", {})
                            
                            if "content" in delta and delta["content"]:
                                yield delta["content"]
                                
                        except json.JSONDecodeError:
                            continue
                            
        except httpx.TimeoutException:
            raise LLMTimeoutError("Stream from Deepseek timed out")
        except httpx.ConnectError:
            raise LLMConnectionError("Failed to connect to Deepseek")
        except Exception as e:
            if isinstance(e, (LLMConnectionError, LLMAuthenticationError, LLMRateLimitError, LLMInvalidRequestError, LLMTimeoutError)):
                raise
            raise LLMConnectionError(f"Unexpected error during streaming: {e}")
    
    async def validate_connection(self) -> bool:
        """
        Validate connection to Deepseek.
        
        Returns:
            True if connection is valid, False otherwise
        """
        try:
            # Make a simple request to validate connection
            response = await self.client.get(f"{self.base_url}/models")
            return response.status_code == 200
        except Exception:
            return False
    
    async def _handle_http_error(self, response: httpx.Response) -> None:
        """Handle HTTP errors from Deepseek API."""
        try:
            error_data = response.json()
            error_message = error_data.get("error", {}).get("message", "Unknown error")
        except:
            error_message = f"HTTP {response.status_code}: {response.text}"
        
        error_class = classify_http_error(response.status_code, error_message)
        raise error_class(error_message, error_code=str(response.status_code))
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()
    
    @property
    def name(self) -> str:
        """Provider name."""
        return "deepseek"
    
    @property
    def supports_streaming(self) -> bool:
        """Whether provider supports streaming."""
        return True
    
    @property
    def supports_tools(self) -> bool:
        """Whether provider supports function calling."""
        return True
