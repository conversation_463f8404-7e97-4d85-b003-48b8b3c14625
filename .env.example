# Arien AI Configuration Template
# Copy this file to .env and customize as needed

# =============================================================================
# LLM Provider Configuration
# =============================================================================

# Deepseek API Configuration
DEEPSEEK_API_KEY=your-deepseek-api-key-here
ARIEN_LLM_PROVIDER=deepseek
ARIEN_LLM_MODEL=deepseek-chat
# Alternative models: deepseek-reasoner

# Ollama Configuration (for local models)
# ARIEN_LLM_PROVIDER=ollama
# ARIEN_LLM_MODEL=llama2
# ARIEN_BASE_URL=http://localhost:11434

# General LLM Settings
ARIEN_MAX_TOKENS=4096
ARIEN_TEMPERATURE=0.7
ARIEN_TIMEOUT=60

# =============================================================================
# Tool Configuration
# =============================================================================

# Enable/Disable Tools
ARIEN_BASH_ENABLED=true
ARIEN_WEB_SEARCH_ENABLED=true

# Bash Tool Settings
ARIEN_BASH_TIMEOUT=300
ARIEN_BASH_MAX_OUTPUT_LINES=1000

# Web Search Tool Settings
ARIEN_WEB_SEARCH_MAX_RESULTS=5
ARIEN_WEB_SEARCH_CACHE_TTL=3600

# =============================================================================
# User Interface Configuration
# =============================================================================

# Progress and Animation
ARIEN_SHOW_PROGRESS=true
ARIEN_SHOW_TIMESTAMPS=true
ARIEN_ANIMATION_SPEED=0.2

# Safety and Confirmation
ARIEN_CONFIRM_DESTRUCTIVE=true

# Output Formatting
ARIEN_COLOR_OUTPUT=true

# =============================================================================
# System Configuration
# =============================================================================

# Logging
ARIEN_LOG_LEVEL=INFO
# ARIEN_LOG_FILE=/path/to/logfile.log

# Retry Logic
ARIEN_MAX_RETRIES=3
ARIEN_BASE_DELAY=1.0
ARIEN_MAX_DELAY=60.0

# =============================================================================
# Advanced Configuration
# =============================================================================

# Custom API Endpoints
# ARIEN_BASE_URL=https://custom-api-endpoint.com/v1

# Configuration Directory
# ARIEN_CONFIG_DIR=/custom/config/path

# =============================================================================
# Environment-Specific Settings
# =============================================================================

# Development
# ARIEN_LOG_LEVEL=DEBUG
# ARIEN_BASH_TIMEOUT=30
# ARIEN_CONFIRM_DESTRUCTIVE=false

# Production
# ARIEN_LOG_LEVEL=WARNING
# ARIEN_BASH_TIMEOUT=600
# ARIEN_CONFIRM_DESTRUCTIVE=true

# Testing
# ARIEN_LOG_LEVEL=ERROR
# ARIEN_BASH_ENABLED=false
# ARIEN_WEB_SEARCH_ENABLED=false
