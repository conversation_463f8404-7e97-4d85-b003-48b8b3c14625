"""
Tests for configuration management.
"""

import os
import tempfile
from pathlib import Path

import pytest

from src.config.settings import Settings, get_settings, reset_settings


@pytest.mark.unit
class TestSettings:
    """Test Settings class."""
    
    def test_default_settings(self):
        """Test default settings values."""
        settings = Settings()
        
        assert settings.llm.provider == "deepseek"
        assert settings.llm.model == "deepseek-chat"
        assert settings.llm.max_tokens == 4096
        assert settings.llm.temperature == 0.7
        assert settings.llm.timeout == 60
        
        assert settings.tools.bash_enabled is True
        assert settings.tools.web_search_enabled is True
        assert settings.tools.bash_timeout == 300
        
        assert settings.ui.show_progress is True
        assert settings.ui.confirm_destructive is True
        
        assert settings.log_level == "INFO"
    
    def test_environment_variable_loading(self, monkeypatch):
        """Test loading settings from environment variables."""
        # Set environment variables
        monkeypatch.setenv("DEEPSEEK_API_KEY", "test-key")
        monkeypatch.setenv("ARIEN_LLM_PROVIDER", "ollama")
        monkeypatch.setenv("ARIEN_LLM_MODEL", "llama2")
        monkeypatch.setenv("ARIEN_MAX_TOKENS", "2048")
        monkeypatch.setenv("ARIEN_TEMPERATURE", "0.5")
        monkeypatch.setenv("ARIEN_BASH_ENABLED", "false")
        monkeypatch.setenv("ARIEN_LOG_LEVEL", "DEBUG")
        
        settings = Settings()
        
        assert settings.llm.api_key == "test-key"
        assert settings.llm.provider == "ollama"
        assert settings.llm.model == "llama2"
        assert settings.llm.max_tokens == 2048
        assert settings.llm.temperature == 0.5
        assert settings.tools.bash_enabled is False
        assert settings.log_level == "DEBUG"
    
    def test_config_directory_creation(self, tmp_path):
        """Test configuration directory creation."""
        config_dir = tmp_path / "test-config"
        settings = Settings()
        settings.config_dir = config_dir
        settings._ensure_config_dir()
        
        assert config_dir.exists()
        assert config_dir.is_dir()
    
    def test_get_api_key(self):
        """Test API key retrieval."""
        settings = Settings()
        settings.llm.api_key = "test-key"
        
        assert settings.get_api_key("deepseek") == "test-key"
        assert settings.get_api_key("ollama") is None
        assert settings.get_api_key("unknown") is None
    
    def test_validation_success(self):
        """Test successful configuration validation."""
        settings = Settings()
        settings.llm.provider = "deepseek"
        settings.llm.api_key = "test-key"
        settings.llm.max_tokens = 1000
        settings.llm.temperature = 0.7
        settings.retry.max_retries = 3
        settings.retry.base_delay = 1.0
        
        errors = settings.validate()
        assert len(errors) == 0
    
    def test_validation_errors(self):
        """Test configuration validation errors."""
        settings = Settings()
        settings.llm.provider = "deepseek"
        settings.llm.api_key = None  # Missing API key
        settings.llm.max_tokens = -1  # Invalid max_tokens
        settings.llm.temperature = 3.0  # Invalid temperature
        settings.retry.max_retries = -1  # Invalid max_retries
        settings.retry.base_delay = -1.0  # Invalid base_delay
        
        errors = settings.validate()
        
        assert len(errors) > 0
        assert any("API key is required" in error for error in errors)
        assert any("max_tokens must be positive" in error for error in errors)
        assert any("temperature must be between 0 and 2" in error for error in errors)
        assert any("max_retries must be non-negative" in error for error in errors)
        assert any("base_delay must be positive" in error for error in errors)
    
    def test_unsupported_provider(self):
        """Test validation with unsupported provider."""
        settings = Settings()
        settings.llm.provider = "unsupported"
        
        errors = settings.validate()
        assert any("Unsupported LLM provider" in error for error in errors)
    
    def test_to_dict(self):
        """Test settings serialization to dictionary."""
        settings = Settings()
        settings.llm.provider = "deepseek"
        settings.llm.model = "deepseek-chat"
        settings.llm.max_tokens = 1000
        
        config_dict = settings.to_dict()
        
        assert isinstance(config_dict, dict)
        assert config_dict["llm"]["provider"] == "deepseek"
        assert config_dict["llm"]["model"] == "deepseek-chat"
        assert config_dict["llm"]["max_tokens"] == 1000
        assert "tools" in config_dict
        assert "retry" in config_dict
        assert "ui" in config_dict


@pytest.mark.unit
class TestGlobalSettings:
    """Test global settings management."""
    
    def test_get_settings_singleton(self):
        """Test that get_settings returns the same instance."""
        reset_settings()
        
        settings1 = get_settings()
        settings2 = get_settings()
        
        assert settings1 is settings2
    
    def test_reset_settings(self):
        """Test settings reset functionality."""
        # Get initial settings
        settings1 = get_settings()
        settings1.llm.model = "modified"
        
        # Reset and get new settings
        reset_settings()
        settings2 = get_settings()
        
        assert settings1 is not settings2
        assert settings2.llm.model == "deepseek-chat"  # Default value
    
    def test_dotenv_loading(self, tmp_path):
        """Test .env file loading."""
        # Create .env file
        env_file = tmp_path / ".env"
        env_content = """
DEEPSEEK_API_KEY=test-from-env
ARIEN_LLM_MODEL=test-model
ARIEN_MAX_TOKENS=512
"""
        env_file.write_text(env_content.strip())
        
        # Change to temp directory
        original_cwd = os.getcwd()
        try:
            os.chdir(tmp_path)
            reset_settings()
            settings = get_settings()
            
            assert settings.llm.api_key == "test-from-env"
            assert settings.llm.model == "test-model"
            assert settings.llm.max_tokens == 512
        finally:
            os.chdir(original_cwd)
