#!/bin/bash

# Arien AI Installation Script
# Supports Linux, macOS, and Windows (WSL)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REPO_URL="https://github.com/arien-ai/arien-ai.git"
INSTALL_DIR="$HOME/.arien-ai"
VENV_DIR="$INSTALL_DIR/venv"
BIN_DIR="$HOME/.local/bin"
LOCAL_INSTALL=false
SOURCE_DIR=""

# Functions
print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                        Arien AI Installer                   ║"
    echo "║          Sophisticated AI-powered CLI Terminal System       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    print_step "Checking system requirements..."
    
    # Check Python version
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
            print_success "Python $PYTHON_VERSION found"
            PYTHON_CMD="python3"
        else
            print_error "Python 3.8+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.8 or higher."
        exit 1
    fi
    
    # Check pip
    if ! $PYTHON_CMD -m pip --version &> /dev/null; then
        print_error "pip not found. Please install pip."
        exit 1
    fi
    
    # Check git
    if ! command -v git &> /dev/null; then
        print_error "git not found. Please install git."
        exit 1
    fi
    
    # Check curl
    if ! command -v curl &> /dev/null; then
        print_warning "curl not found. Some features may not work properly."
    fi
    
    print_success "All requirements satisfied"
}

detect_platform() {
    print_step "Detecting platform..."
    
    case "$(uname -s)" in
        Linux*)
            PLATFORM="Linux"
            if grep -q Microsoft /proc/version 2>/dev/null; then
                PLATFORM="WSL"
            fi
            ;;
        Darwin*)
            PLATFORM="macOS"
            ;;
        CYGWIN*|MINGW32*|MSYS*|MINGW*)
            PLATFORM="Windows"
            ;;
        *)
            PLATFORM="Unknown"
            ;;
    esac
    
    print_success "Platform detected: $PLATFORM"
}

backup_existing_installation() {
    if [ -d "$INSTALL_DIR" ]; then
        print_step "Backing up existing installation..."
        BACKUP_DIR="${INSTALL_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
        mv "$INSTALL_DIR" "$BACKUP_DIR"
        print_success "Existing installation backed up to $BACKUP_DIR"
    fi
}

install_arien_ai() {
    print_step "Installing Arien AI..."

    # Create installation directory
    mkdir -p "$INSTALL_DIR"

    if [ "$LOCAL_INSTALL" = true ]; then
        # Local installation - copy source files
        print_step "Copying source files from $SOURCE_DIR..."

        # Verify source directory has required files
        if [ ! -f "$SOURCE_DIR/pyproject.toml" ]; then
            print_error "pyproject.toml not found in source directory. Is this an Arien AI project?"
            exit 1
        fi

        if [ ! -f "$SOURCE_DIR/requirements.txt" ]; then
            print_error "requirements.txt not found in source directory."
            exit 1
        fi

        if [ ! -d "$SOURCE_DIR/src" ]; then
            print_error "src directory not found in source directory."
            exit 1
        fi

        # Copy all files to installation directory
        cp -r "$SOURCE_DIR"/* "$INSTALL_DIR/"
        cd "$INSTALL_DIR"
    else
        # Remote installation - clone repository
        cd "$INSTALL_DIR"
        print_step "Cloning repository..."
        git clone "$REPO_URL" .
    fi

    # Create virtual environment
    print_step "Creating virtual environment..."
    $PYTHON_CMD -m venv "$VENV_DIR"

    # Activate virtual environment
    source "$VENV_DIR/bin/activate"

    # Upgrade pip
    print_step "Upgrading pip..."
    pip install --upgrade pip

    # Install dependencies
    print_step "Installing dependencies..."
    pip install -r requirements.txt

    # Install Arien AI in development mode
    print_step "Installing Arien AI..."
    pip install -e .

    print_success "Arien AI installed successfully"
}

create_launcher_script() {
    print_step "Creating launcher script..."
    
    # Create bin directory
    mkdir -p "$BIN_DIR"
    
    # Create launcher script
    cat > "$BIN_DIR/arien" << EOF
#!/bin/bash
# Arien AI Launcher Script

# Activate virtual environment and run Arien AI
source "$VENV_DIR/bin/activate"
exec python -m src.main "\$@"
EOF
    
    # Make executable
    chmod +x "$BIN_DIR/arien"
    
    print_success "Launcher script created at $BIN_DIR/arien"
}

setup_shell_integration() {
    print_step "Setting up shell integration..."
    
    # Detect shell
    SHELL_NAME=$(basename "$SHELL")
    
    case "$SHELL_NAME" in
        bash)
            SHELL_RC="$HOME/.bashrc"
            ;;
        zsh)
            SHELL_RC="$HOME/.zshrc"
            ;;
        fish)
            SHELL_RC="$HOME/.config/fish/config.fish"
            ;;
        *)
            print_warning "Unknown shell: $SHELL_NAME. Manual PATH setup may be required."
            return
            ;;
    esac
    
    # Add bin directory to PATH if not already present
    if [ -f "$SHELL_RC" ]; then
        if ! grep -q "$BIN_DIR" "$SHELL_RC"; then
            echo "" >> "$SHELL_RC"
            echo "# Arien AI" >> "$SHELL_RC"
            echo "export PATH=\"$BIN_DIR:\$PATH\"" >> "$SHELL_RC"
            print_success "Added $BIN_DIR to PATH in $SHELL_RC"
        else
            print_success "PATH already configured in $SHELL_RC"
        fi
    else
        print_warning "$SHELL_RC not found. Please add $BIN_DIR to your PATH manually."
    fi
}

create_config_template() {
    print_step "Creating configuration template..."
    
    CONFIG_FILE="$INSTALL_DIR/.env.example"
    cat > "$CONFIG_FILE" << EOF
# Arien AI Configuration Template
# Copy this file to .env and customize as needed

# LLM Provider Configuration
DEEPSEEK_API_KEY=your-deepseek-api-key-here
ARIEN_LLM_PROVIDER=deepseek
ARIEN_LLM_MODEL=deepseek-chat
ARIEN_MAX_TOKENS=4096
ARIEN_TEMPERATURE=0.7

# Tool Configuration
ARIEN_BASH_ENABLED=true
ARIEN_WEB_SEARCH_ENABLED=true
ARIEN_BASH_TIMEOUT=300

# UI Configuration
ARIEN_SHOW_PROGRESS=true
ARIEN_CONFIRM_DESTRUCTIVE=true
ARIEN_COLOR_OUTPUT=true

# System Configuration
ARIEN_LOG_LEVEL=INFO
ARIEN_MAX_RETRIES=3
EOF
    
    print_success "Configuration template created at $CONFIG_FILE"
}

run_post_install_checks() {
    print_step "Running post-installation checks..."
    
    # Test installation
    source "$VENV_DIR/bin/activate"
    
    if python -m src.main version &> /dev/null; then
        print_success "Installation test passed"
    else
        print_error "Installation test failed"
        exit 1
    fi
    
    # Check if arien command is available
    if [ -x "$BIN_DIR/arien" ]; then
        print_success "Arien command available at $BIN_DIR/arien"
    else
        print_error "Arien command not found"
        exit 1
    fi
}

print_completion_message() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                   Installation Complete!                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    if [ "$LOCAL_INSTALL" = true ]; then
        echo -e "${BLUE}Installation Mode:${NC} Local (from $SOURCE_DIR)"
    else
        echo -e "${BLUE}Installation Mode:${NC} Remote (from GitHub)"
    fi
    echo ""

    echo -e "${BLUE}Next Steps:${NC}"
    echo "1. Restart your terminal or run: source ~/.bashrc (or ~/.zshrc)"
    echo "2. Set your Deepseek API key: export DEEPSEEK_API_KEY='your-key'"
    echo "3. Run Arien AI: arien"
    echo ""
    echo -e "${BLUE}Configuration:${NC}"
    echo "• Installation directory: $INSTALL_DIR"
    echo "• Configuration template: $INSTALL_DIR/.env.example"
    echo "• Launcher script: $BIN_DIR/arien"
    echo ""
    echo -e "${BLUE}Commands:${NC}"
    echo "• arien                    # Interactive mode"
    echo "• arien run 'command'      # Single command mode"
    echo "• arien config             # Show configuration"
    echo "• arien validate           # Validate setup"
    echo ""
    echo -e "${YELLOW}Need help?${NC} Visit: https://github.com/arien-ai/arien-ai"
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --local)
                LOCAL_INSTALL=true
                SOURCE_DIR="$(pwd)"
                print_step "Local installation mode enabled"
                print_step "Source directory: $SOURCE_DIR"
                shift
                ;;
            --source-dir)
                LOCAL_INSTALL=true
                SOURCE_DIR="$2"
                if [ ! -d "$SOURCE_DIR" ]; then
                    print_error "Source directory does not exist: $SOURCE_DIR"
                    exit 1
                fi
                print_step "Local installation mode enabled"
                print_step "Source directory: $SOURCE_DIR"
                shift 2
                ;;
            --help|-h)
                print_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                print_usage
                exit 1
                ;;
        esac
    done
}

print_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --local              Install from current directory"
    echo "  --source-dir DIR     Install from specified directory"
    echo "  --help, -h           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                   # Install from GitHub repository"
    echo "  $0 --local          # Install from current directory"
    echo "  $0 --source-dir /path/to/arien-ai  # Install from specific directory"
}

# Main installation flow
main() {
    print_header

    # Parse command line arguments
    parse_arguments "$@"

    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        print_error "Please do not run this script as root"
        exit 1
    fi

    check_requirements
    detect_platform
    backup_existing_installation
    install_arien_ai
    create_launcher_script
    setup_shell_integration
    create_config_template
    run_post_install_checks
    print_completion_message
}

# Handle interruption
trap 'echo -e "\n${RED}Installation interrupted${NC}"; exit 1' INT TERM

# Run main function
main "$@"
