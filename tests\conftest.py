"""
Pytest configuration and fixtures for Arien AI tests.
"""

import asyncio
import os
import tempfile
from pathlib import Path
from typing import Generator, AsyncGenerator
from unittest.mock import Mock, AsyncMock

import pytest

from src.config.settings import Settings, reset_settings
from src.core.agent import ArienAgent
from src.providers.base import Base<PERSON><PERSON><PERSON>ider, LLMResponse, Message, MessageRole
from src.tools.base import <PERSON><PERSON>ool, ToolResult, ToolStatus


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def mock_settings() -> Generator[Settings, None, None]:
    """Create mock settings for testing."""
    # Reset global settings
    reset_settings()
    
    # Create test settings
    settings = Settings()
    settings.llm.provider = "deepseek"
    settings.llm.model = "deepseek-chat"
    settings.llm.api_key = "test-api-key"
    settings.llm.max_tokens = 1000
    settings.llm.temperature = 0.7
    
    settings.tools.bash_enabled = True
    settings.tools.web_search_enabled = True
    settings.tools.bash_timeout = 30
    
    settings.ui.show_progress = False
    settings.ui.confirm_destructive = False
    
    yield settings
    
    # Reset after test
    reset_settings()


@pytest.fixture
def mock_llm_provider() -> Mock:
    """Create a mock LLM provider for testing."""
    provider = Mock(spec=BaseLLMProvider)
    provider.name = "mock"
    provider.supports_streaming = True
    provider.supports_tools = True
    
    # Mock async methods
    provider.generate_response = AsyncMock()
    provider.stream_response = AsyncMock()
    provider.validate_connection = AsyncMock(return_value=True)
    
    # Default response
    provider.generate_response.return_value = LLMResponse(
        content="Mock response",
        tool_calls=[],
        finish_reason="stop",
        usage={"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15},
        model="mock-model"
    )
    
    return provider


@pytest.fixture
def mock_tool() -> Mock:
    """Create a mock tool for testing."""
    tool = Mock(spec=BaseTool)
    tool.name = "mock_tool"
    tool.description = "A mock tool for testing"
    tool.parameters = {
        "type": "object",
        "properties": {
            "input": {"type": "string", "description": "Test input"}
        },
        "required": ["input"]
    }
    tool.can_run_parallel = True
    tool.is_destructive = False
    tool.requires_confirmation = False
    
    # Mock async execute method
    tool.execute = AsyncMock()
    tool.execute.return_value = ToolResult(
        status=ToolStatus.SUCCESS,
        output="Mock tool output",
        execution_time=0.1
    )
    
    # Mock other methods
    tool.get_schema.return_value = {
        "type": "function",
        "function": {
            "name": tool.name,
            "description": tool.description,
            "parameters": tool.parameters
        }
    }
    tool.validate_parameters.return_value = []
    
    return tool


@pytest.fixture
async def mock_agent(mock_settings: Settings, mock_llm_provider: Mock) -> AsyncGenerator[ArienAgent, None]:
    """Create a mock agent for testing."""
    # Create agent with mock provider
    agent = ArienAgent(mock_settings)
    agent.llm_provider = mock_llm_provider
    
    yield agent
    
    # Cleanup
    await agent.cleanup()


@pytest.fixture
def sample_messages() -> list[Message]:
    """Create sample messages for testing."""
    return [
        Message(role=MessageRole.SYSTEM, content="You are a helpful assistant."),
        Message(role=MessageRole.USER, content="Hello, how are you?"),
        Message(role=MessageRole.ASSISTANT, content="I'm doing well, thank you!"),
    ]


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Setup test environment variables."""
    # Set test environment variables
    monkeypatch.setenv("ARIEN_LOG_LEVEL", "DEBUG")
    monkeypatch.setenv("ARIEN_BASH_TIMEOUT", "10")
    monkeypatch.setenv("ARIEN_WEB_SEARCH_MAX_RESULTS", "3")
    
    # Remove API keys to prevent accidental real API calls
    monkeypatch.delenv("DEEPSEEK_API_KEY", raising=False)
    monkeypatch.delenv("ARIEN_API_KEY", raising=False)


@pytest.fixture
def mock_http_response():
    """Create a mock HTTP response for testing."""
    class MockResponse:
        def __init__(self, json_data, status_code=200):
            self.json_data = json_data
            self.status_code = status_code
            self.text = str(json_data)
        
        def json(self):
            return self.json_data
        
        def raise_for_status(self):
            if self.status_code >= 400:
                raise Exception(f"HTTP {self.status_code}")
    
    return MockResponse


# Markers for test categorization
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.slow = pytest.mark.slow
