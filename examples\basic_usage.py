#!/usr/bin/env python3
"""
Basic usage examples for Arien AI.

This script demonstrates how to use Arien AI programmatically
and showcases various features and capabilities.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.config.settings import Settings
from src.core.agent import ArienAgent
from src.ui.formatter import OutputFormatter


async def basic_agent_usage():
    """Demonstrate basic agent usage."""
    print("🤖 Basic Arien AI Agent Usage Example\n")
    
    # Create settings
    settings = Settings()
    settings.llm.provider = "deepseek"
    settings.llm.model = "deepseek-chat"
    settings.llm.api_key = os.getenv("DEEPSEEK_API_KEY", "demo-key")
    
    # Disable confirmations for demo
    settings.ui.confirm_destructive = False
    
    # Create formatter for output
    formatter = OutputFormatter()
    
    try:
        # Create agent
        agent = ArienAgent(settings)
        
        # Example requests
        examples = [
            "List the files in the current directory",
            "What is the current date and time?",
            "Search for the latest Python 3.12 features",
            "Create a simple Python hello world script",
            "Show system information like CPU and memory usage",
        ]
        
        for i, request in enumerate(examples, 1):
            print(f"\n{'='*60}")
            print(f"Example {i}: {request}")
            print('='*60)
            
            try:
                # Process request
                response = await agent.process_request(request)
                
                # Display response
                formatter.format_ai_response(response.content, settings.llm.model)
                
                # Display tool results
                for tool_execution in response.tool_results:
                    formatter.format_tool_result(
                        tool_execution.tool_name,
                        tool_execution.result
                    )
                
            except Exception as e:
                formatter.format_error(f"Request failed: {str(e)}")
        
        # Cleanup
        await agent.cleanup()
        
    except Exception as e:
        formatter.format_error(f"Agent initialization failed: {str(e)}")


async def tool_specific_examples():
    """Demonstrate tool-specific usage."""
    print("\n🛠️ Tool-Specific Examples\n")
    
    from src.tools.bash_tool import BashTool
    from src.tools.web_search_tool import WebSearchTool
    
    formatter = OutputFormatter()
    
    # Bash tool examples
    print("\n📟 Bash Tool Examples")
    print("-" * 30)
    
    bash_tool = BashTool()
    
    bash_examples = [
        {"command": "echo 'Hello from Arien AI!'"},
        {"command": "python --version"},
        {"command": "ls -la", "working_directory": "."},
    ]
    
    for example in bash_examples:
        print(f"\nExecuting: {example['command']}")
        try:
            result = await bash_tool.execute(**example)
            formatter.format_tool_result("bash", result)
        except Exception as e:
            formatter.format_error(f"Bash execution failed: {str(e)}")
    
    # Web search tool examples
    print("\n🔍 Web Search Tool Examples")
    print("-" * 30)
    
    web_tool = WebSearchTool()
    
    search_examples = [
        {"query": "Python 3.12 new features", "max_results": 3},
        {"query": "artificial intelligence latest news", "max_results": 2},
    ]
    
    for example in search_examples:
        print(f"\nSearching: {example['query']}")
        try:
            result = await web_tool.execute(**example)
            formatter.format_tool_result("web_search", result)
        except Exception as e:
            formatter.format_error(f"Web search failed: {str(e)}")
    
    # Cleanup
    await web_tool.__aexit__(None, None, None)


async def configuration_examples():
    """Demonstrate configuration options."""
    print("\n⚙️ Configuration Examples\n")
    
    formatter = OutputFormatter()
    
    # Show default configuration
    settings = Settings()
    formatter.format_info("Default Configuration:", "Settings")
    
    config_dict = settings.to_dict()
    for section, values in config_dict.items():
        if isinstance(values, dict):
            print(f"\n{section.upper()}:")
            for key, value in values.items():
                print(f"  {key}: {value}")
        else:
            print(f"{section}: {values}")
    
    # Show validation
    errors = settings.validate()
    if errors:
        formatter.format_warning("Configuration Issues:", "Validation")
        for error in errors:
            print(f"  • {error}")
    else:
        formatter.format_success("Configuration is valid!", "Validation")


async def error_handling_examples():
    """Demonstrate error handling."""
    print("\n🚨 Error Handling Examples\n")
    
    from src.tools.bash_tool import BashTool
    from src.core.exceptions import BashExecutionError
    
    formatter = OutputFormatter()
    bash_tool = BashTool()
    
    # Example of command that will fail
    print("Testing error handling with invalid command:")
    try:
        result = await bash_tool.execute(command="nonexistent_command_12345")
        formatter.format_tool_result("bash", result)
    except Exception as e:
        formatter.format_error(f"Expected error: {str(e)}")
    
    # Example of parameter validation error
    print("\nTesting parameter validation:")
    try:
        result = await bash_tool.execute()  # Missing required parameter
        formatter.format_tool_result("bash", result)
    except Exception as e:
        formatter.format_error(f"Parameter validation error: {str(e)}")


async def main():
    """Main example runner."""
    print("🚀 Arien AI Examples")
    print("=" * 50)
    
    # Check if API key is available
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("⚠️  Warning: DEEPSEEK_API_KEY not set. Some examples may fail.")
        print("   Set your API key: export DEEPSEEK_API_KEY='your-key-here'")
        print()
    
    try:
        # Run examples
        await configuration_examples()
        await tool_specific_examples()
        await error_handling_examples()
        
        # Only run agent examples if API key is available
        if os.getenv("DEEPSEEK_API_KEY"):
            await basic_agent_usage()
        else:
            print("\n🤖 Skipping agent examples (no API key)")
        
        print("\n✅ All examples completed!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Examples failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Run examples
    asyncio.run(main())
