#!/usr/bin/env python3
"""
Advanced features demonstration for Arien AI.

This script showcases the advanced capabilities including:
- Configuration profiles
- Context management
- File operations
- Performance monitoring
- Error handling strategies
"""

import asyncio
import os
import sys
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.config.settings import Settings
from src.config.profiles import get_profile_manager
from src.core.agent import ArienAgent
from src.core.context import ConversationContext
from src.core.logging import setup_logging, performance_logger
from src.tools.file_tool import FileTool
from src.tools.bash_tool import BashTool
from src.tools.web_search_tool import WebSearchTool
from src.ui.formatter import OutputFormatter


async def demonstrate_profiles():
    """Demonstrate configuration profiles."""
    print("🎯 Configuration Profiles Demo")
    print("=" * 50)
    
    profile_manager = get_profile_manager()
    
    # List all profiles
    print("\n📋 Available Profiles:")
    profiles = profile_manager.list_profiles()
    for name, profile in profiles.items():
        print(f"  • {name}: {profile.description}")
    
    # Show profile recommendations
    print("\n🎯 Profile Recommendations:")
    use_cases = [
        "I want to develop Python applications",
        "I need to research AI trends",
        "I'm doing system administration",
        "I want to learn programming"
    ]
    
    for use_case in use_cases:
        recommendations = profile_manager.get_profile_recommendations(use_case)
        print(f"\nUse case: {use_case}")
        for rec in recommendations[:2]:  # Show top 2
            print(f"  → {rec.name}: {rec.description}")
    
    # Create custom profile
    print("\n🔧 Creating Custom Profile:")
    custom_profile = profile_manager.create_custom_profile(
        name="demo_custom",
        description="Custom profile for demonstration",
        base_profile="development",
        overrides={
            "llm.temperature": 0.5,
            "ui.show_progress": True,
        },
        tags=["demo", "custom"]
    )
    print(f"Created: {custom_profile.name}")
    print(f"Description: {custom_profile.description}")


async def demonstrate_context_management():
    """Demonstrate conversation context management."""
    print("\n🧠 Context Management Demo")
    print("=" * 50)
    
    context = ConversationContext()
    
    # Add some conversation turns
    context.add_system_message("You are a helpful assistant.")
    context.add_user_message("Hello, can you help me with Python?")
    context.add_assistant_message("Of course! I'd be happy to help you with Python programming.")
    
    # Add a conversation turn with tool execution
    context.add_conversation_turn(
        user_input="Show me the current directory",
        ai_response="I'll list the current directory contents for you.",
        tool_executions=[{
            "tool_name": "bash",
            "parameters": {"command": "ls -la"},
            "result": {
                "status": "success",
                "output": "total 8\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 12:00 .\ndrwxr-xr-x 3 <USER> <GROUP> 4096 Jan 1 12:00 ..",
                "error": None,
                "execution_time": 0.1
            }
        }]
    )
    
    # Show context statistics
    stats = context.get_context_stats()
    print("\n📊 Context Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Generate context summary
    summary = context.generate_context_summary()
    print(f"\n📝 Context Summary: {summary}")
    
    # Get working context
    working_context = context.get_working_context()
    print("\n🔧 Working Context:")
    for key, value in working_context.items():
        print(f"  {key}: {value}")


async def demonstrate_file_operations():
    """Demonstrate file tool operations."""
    print("\n📁 File Operations Demo")
    print("=" * 50)
    
    file_tool = FileTool()
    formatter = OutputFormatter()
    
    # Create a temporary directory for demo
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create a test file
        test_file = temp_path / "demo.txt"
        result = await file_tool.execute(
            operation="write",
            path=str(test_file),
            content="Hello, Arien AI!\nThis is a demonstration file.\n"
        )
        formatter.format_tool_result("file", result)
        
        # Read the file
        result = await file_tool.execute(
            operation="read",
            path=str(test_file)
        )
        formatter.format_tool_result("file", result)
        
        # List directory contents
        result = await file_tool.execute(
            operation="list",
            path=str(temp_path),
            pattern="*.txt"
        )
        formatter.format_tool_result("file", result)
        
        # Get file info
        result = await file_tool.execute(
            operation="info",
            path=str(test_file)
        )
        formatter.format_tool_result("file", result)
        
        # Copy file
        backup_file = temp_path / "demo_backup.txt"
        result = await file_tool.execute(
            operation="copy",
            source=str(test_file),
            destination=str(backup_file)
        )
        formatter.format_tool_result("file", result)


async def demonstrate_performance_monitoring():
    """Demonstrate performance monitoring."""
    print("\n⚡ Performance Monitoring Demo")
    print("=" * 50)
    
    # Simulate some operations with performance logging
    import time
    
    # Simulate tool execution
    start_time = time.time()
    await asyncio.sleep(0.1)  # Simulate work
    duration = time.time() - start_time
    
    performance_logger.log_tool_usage(
        tool_name="demo_tool",
        success=True,
        duration=duration,
        operation="demo_operation"
    )
    
    # Simulate LLM usage
    performance_logger.log_llm_usage(
        provider="deepseek",
        model="deepseek-chat",
        tokens=150,
        duration=0.5,
        request_type="completion"
    )
    
    # Get metrics summary
    metrics = performance_logger.get_metrics_summary()
    print("\n📈 Performance Metrics:")
    for operation, stats in metrics.items():
        print(f"\n{operation}:")
        for metric, value in stats.items():
            if isinstance(value, float):
                print(f"  {metric}: {value:.3f}s")
            else:
                print(f"  {metric}: {value}")


async def demonstrate_error_handling():
    """Demonstrate error handling strategies."""
    print("\n🚨 Error Handling Demo")
    print("=" * 50)
    
    bash_tool = BashTool()
    formatter = OutputFormatter()
    
    # Test various error scenarios
    error_scenarios = [
        {
            "name": "Invalid Command",
            "params": {"command": "nonexistent_command_xyz123"}
        },
        {
            "name": "Missing Parameters",
            "params": {}
        },
        {
            "name": "Permission Denied (simulated)",
            "params": {"command": "echo 'This would normally work'"}
        }
    ]
    
    for scenario in error_scenarios:
        print(f"\n🔍 Testing: {scenario['name']}")
        try:
            result = await bash_tool.execute(**scenario['params'])
            formatter.format_tool_result("bash", result)
        except Exception as e:
            formatter.format_error(f"Exception caught: {str(e)}")


async def demonstrate_intelligent_execution():
    """Demonstrate intelligent execution strategies."""
    print("\n🧩 Intelligent Execution Demo")
    print("=" * 50)
    
    # This would normally be done by the agent, but we'll simulate it
    from src.core.agent import ArienAgent
    
    # Create a mock agent with demo settings
    settings = Settings()
    settings.llm.api_key = "demo-key"  # Won't actually call API
    
    print("\n🤖 Agent Execution Strategy Analysis:")
    print("The agent analyzes tool calls to determine optimal execution:")
    print("  • Parallel: Independent read-only operations")
    print("  • Sequential: Dependent or potentially conflicting operations")
    print("  • User confirmation: Destructive or dangerous operations")
    
    # Show tool capabilities
    tools = {
        "bash": BashTool(),
        "file": FileTool(),
        "web_search": WebSearchTool()
    }
    
    print("\n🛠️ Available Tools:")
    for name, tool in tools.items():
        print(f"  • {name}:")
        print(f"    - Parallel capable: {tool.can_run_parallel}")
        print(f"    - Destructive: {tool.is_destructive}")
        print(f"    - Requires confirmation: {tool.requires_confirmation}")


async def demonstrate_session_management():
    """Demonstrate session management."""
    print("\n💾 Session Management Demo")
    print("=" * 50)
    
    context = ConversationContext()
    
    # Simulate a conversation session
    context.add_user_message("Help me create a Python script")
    context.add_assistant_message("I'll help you create a Python script. What should it do?")
    context.add_user_message("Make it print 'Hello World'")
    context.add_assistant_message("Here's a simple Python script that prints 'Hello World'")
    
    # Add tool execution
    context.add_conversation_turn(
        user_input="Create the file",
        ai_response="I've created the hello.py file for you.",
        tool_executions=[{
            "tool_name": "file",
            "parameters": {"operation": "write", "path": "hello.py", "content": "print('Hello World')"},
            "result": {
                "status": "success",
                "output": "File created successfully",
                "error": None,
                "execution_time": 0.05
            }
        }]
    )
    
    # Show session info
    working_context = context.get_working_context()
    print("\n📋 Session Information:")
    for key, value in working_context.items():
        print(f"  {key}: {value}")
    
    # Save session (to temporary file)
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        context.save_session(f.name)
        print(f"\n💾 Session saved to: {f.name}")
        
        # Load session back
        new_context = ConversationContext()
        new_context.load_session(f.name)
        print(f"📂 Session loaded successfully")
        print(f"   Conversation turns: {len(new_context.conversation_turns)}")
        
        # Cleanup
        os.unlink(f.name)


async def main():
    """Main demonstration runner."""
    print("🚀 Arien AI Advanced Features Demonstration")
    print("=" * 60)
    
    # Setup logging for demo
    setup_logging("INFO", enable_rich=True, enable_performance=True)
    
    try:
        # Run all demonstrations
        await demonstrate_profiles()
        await demonstrate_context_management()
        await demonstrate_file_operations()
        await demonstrate_performance_monitoring()
        await demonstrate_error_handling()
        await demonstrate_intelligent_execution()
        await demonstrate_session_management()
        
        print("\n✅ All demonstrations completed successfully!")
        print("\n🎯 Key Takeaways:")
        print("  • Configuration profiles enable use-case optimization")
        print("  • Context management provides intelligent conversation handling")
        print("  • File operations are secure and comprehensive")
        print("  • Performance monitoring enables optimization")
        print("  • Error handling is robust and user-friendly")
        print("  • Execution strategies are intelligent and safe")
        print("  • Session management preserves conversation state")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demonstration interrupted by user")
    except Exception as e:
        print(f"\n❌ Demonstration failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Run demonstration
    asyncio.run(main())
